import { NextRequest, NextResponse } from 'next/server';
import { withManagerProtection } from '@/lib/auth/middleware';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

/**
 * Report parameters schema
 */
const reportParamsSchema = z.object({
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  type: z.enum(['AMC', 'WARRANTY', 'SERVICE', 'SALES']).optional(),
});

/**
 * GET /api/reports/summary
 * Get summary report data
 * Manager-only endpoint (accessible by managers and admins)
 */
export const GET = withManagerProtection(async (request: NextRequest) => {
  try {
    const { searchParams } = new URL(request.url);

    // Parse and validate parameters
    const params = {
      startDate: searchParams.get('startDate') || undefined,
      endDate: searchParams.get('endDate') || undefined,
      type: searchParams.get('type') || undefined,
    };

    const validatedParams = reportParamsSchema.parse(params);

    // Parse dates - use a wider range for better data visibility
    const startDate = validatedParams.startDate ? new Date(validatedParams.startDate) : new Date(2020, 0, 1);
    const endDate = validatedParams.endDate ? new Date(validatedParams.endDate) : new Date();

    // Get summary data based on report type
    let summary: any = {};
    
    if (!validatedParams.type || validatedParams.type === 'AMC') {
      // Get AMC summary - use proper date fields and remove createdAt filter for better data visibility
      const amcContracts = await prisma.amc_contracts.count();

      const activeAmcContracts = await prisma.amc_contracts.count({
        where: {
          status: 'ACTIVE',
        },
      });

      const expiringAmcContracts = await prisma.amc_contracts.count({
        where: {
          status: 'ACTIVE',
          endDate: {
            lte: new Date(new Date().setDate(new Date().getDate() + 30)),
          },
        },
      });

      summary.amc = {
        total: amcContracts,
        active: activeAmcContracts,
        expiring: expiringAmcContracts,
      };
    }
    
    if (!validatedParams.type || validatedParams.type === 'WARRANTY') {
      // Get warranty summary - use correct field names from schema
      const warranties = await prisma.warranties.count();

      // Check what status values actually exist in the database
      const activeWarranties = await prisma.warranties.count({
        where: {
          warrantyDate: {
            gte: new Date(), // Active means warranty is still valid
          },
        },
      });

      const expiringWarranties = await prisma.warranties.count({
        where: {
          warrantyDate: {
            gte: new Date(),
            lte: new Date(new Date().setDate(new Date().getDate() + 30)),
          },
        },
      });

      summary.warranty = {
        total: warranties,
        active: activeWarranties,
        expiring: expiringWarranties,
      };
    }
    
    if (!validatedParams.type || validatedParams.type === 'SERVICE') {
      // Get service summary - use correct status values
      const serviceReports = await prisma.service_reports.count();

      const pendingServiceReports = await prisma.service_reports.count({
        where: {
          status: 'OPEN', // Based on schema, default status is 'OPEN'
        },
      });

      const completedServiceReports = await prisma.service_reports.count({
        where: {
          status: 'CLOSED',
        },
      });

      summary.service = {
        total: serviceReports,
        pending: pendingServiceReports,
        completed: completedServiceReports,
      };
    }
    
    if (!validatedParams.type || validatedParams.type === 'SALES') {
      // Get sales summary - remove date filters for better data visibility
      const salesLeads = await prisma.sales_leads.count();

      const salesOpportunities = await prisma.sales_opportunities.count();

      const salesOrders = await prisma.sales_orders.count();

      summary.sales = {
        leads: salesLeads,
        opportunities: salesOpportunities,
        orders: salesOrders,
      };
    }
    
    return NextResponse.json({
      summary,
      period: {
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
      },
      success: true,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      console.error('Validation error in report summary:', error.errors);
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Error generating report summary:', error);
    return NextResponse.json(
      {
        error: 'Failed to generate report summary',
        details: error instanceof Error ? error.message : 'Unknown error',
        success: false,
      },
      { status: 500 }
    );
  }
});
